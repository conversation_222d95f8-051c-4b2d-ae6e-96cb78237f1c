import base64
import json
import psutil
import requests
import subprocess
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor
import zhixuewang
from app.config import Config
import logging
from logger import Logger

# 初始化日志系统
log = Logger()
logger = log.get_logger(filename="zhixue")

def is_process_running(process_name):
    """检查是否有正在运行的进程包含给定的名称。"""
    for proc in psutil.process_iter(['name']):
        try:
            if process_name.lower() in proc.info['name'].lower():
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return False

def start_executable():
    """启动验证码API可执行文件"""
    process_name = 'geetest_api_packed.exe'

    if not is_process_running(process_name):
        try:
            # 使用 start 命令启动可执行文件
            command = f'start {process_name}'
            subprocess.Popen(command, shell=True)
            logger.info("等待验证码API启动...")
            time.sleep(3)
            logger.info("可执行文件启动成功。")
        except Exception as e:
            logger.error(f"启动可执行文件时发生错误：{e}")
    else:
        logger.info(f"{process_name} 已在运行。")

class ZhiXueException(Exception):
    """智学网操作异常类"""
    pass

class ZhiXue:
    """智学网操作类"""
    
    def __init__(self, user_account=None, password=None):
        """初始化智学网操作类
        
        Args:
            user_account: 用户账号
            password: 用户密码
        """
        # 创建配置实例
        self.config = Config()
        
        # 从配置获取参数，如果未提供则使用默认值
        self.user_account = user_account or self.config.get_nested('ZHIXUE.user_account', "")
        self.password = password or self.config.get_nested('ZHIXUE.password', "")
        self.user_id = None

    def login_student(self) -> requests.Session:
        """登录智学网学生账号
        
        Returns:
            requests.Session: 登录后的会话对象
        """
        session = zhixuewang.session.get_basic_session()

        try:
            captcha_data, captcha_id = self._login_step_0(session)
            lt, execution = self._login_step_1(session)
            st = self._login_step_2(session, lt, execution, captcha_data, captcha_id)
            self._login_step_3(session, st)
            logger.info("智学网登录成功")
            return session
        except ZhiXueException as e:
            logger.error(f"智学网登录失败: {e}")
            raise

    def _login_step_0(self, session: requests.Session) -> tuple:
        """登录步骤0：获取验证码
        
        Args:
            session: 会话对象
            
        Returns:
            tuple: 验证码数据和ID
        """
        logger.info("    [-1/6] 初始化登录.")
        for i in range(3):
            try:
                time.sleep(0.5)
                captcha_data = requests.get("http://127.0.0.1:8080/get_geetest", timeout=10).json()["data"]
                if captcha_data["result"] != "success":
                    raise ZhiXueException("登录智学网异常: 验证码获取失败.")
                break
            except Exception as exc:
                logger.error(f"验证码获取失败，尝试次数 {i+1}/3: {exc}")
        else:
            raise ZhiXueException("登录智学网异常: 验证码获取失败.")

        url = "https://www.zhixue.com/edition/login?from=web_login"
        data = {
            "appId": "zx-container-client",
            "captchaType": "third",
            "thirdCaptchaExtInfo[lot_number]": captcha_data["seccode"]["lot_number"],
            "thirdCaptchaExtInfo[pass_token]": captcha_data["seccode"]["pass_token"],
            "thirdCaptchaExtInfo[gen_time]": captcha_data["seccode"]["gen_time"],
            "thirdCaptchaExtInfo[captcha_output]": captcha_data["seccode"]["captcha_output"],
            "loginName": self.user_account,
            "password": self.password,
        }
        result = session.post(url, params={"from": "web_login"}, data=data).json()
        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['message']}")

        self.user_id = result["data"]["userId"]
        return (captcha_data, result["data"]["captchaId"])

    def _login_step_1(self, session: requests.Session) -> tuple:
        """登录步骤1：发送登录请求
        
        Args:
            session: 会话对象
            
        Returns:
            tuple: lt和execution参数
        """
        logger.info("    [0/6] 发送登录请求.")
        if "&" in self.password:
            raise ZhiXueException("登录智学网异常: 不支持登录密码包含 & 的账号.")

        url = "https://sso.zhixue.com/sso_alpha/login"
        data = {"service": "https://www.zhixue.com:443/ssoservice.jsp"}
        result = session.get(url, params=data).text
        result = json.loads(result.split("('", 1)[1].split("')")[0].replace("\\", ""))

        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['data']}")

        if "st" in result["data"]:
            raise ZhiXueException("登录智学网异常: 此会话已登录.")

        lt = result["data"]["lt"]
        execution = result["data"]["execution"]
        return (lt, execution)

    def _login_step_2(self, session: requests.Session, lt: str, execution: str, captcha_data: dict, captcha_id: str) -> str:
        """登录步骤2：发送账号密码
        
        Args:
            session: 会话对象
            lt: lt参数
            execution: execution参数
            captcha_data: 验证码数据
            captcha_id: 验证码ID
            
        Returns:
            str: st参数
        """
        logger.info("    [1/6] 发送账号密码.")
        url = "https://sso.zhixue.com/sso_alpha/login"
        data = {
            "service": "https://www.zhixue.com:443/ssoservice.jsp",
            "captchaId": captcha_id,
            "captchaType": "third",
            "thirdCaptchaParam": captcha_data["seccode"],
            "version": "v3",
            "_eventId": "submit",
            "key": "auto",
            "lt": lt,
            "execution": execution,
            "username": self.user_account,
            "password": self.password,
        }
        result = session.get(url, params=data).text
        result = json.loads(result.split("('", 1)[1].split("')")[0].replace("\\", ""))

        if result["result"] != "success":
            raise ZhiXueException(f"登录智学网异常: {result['data']}")

        if "st" not in result["data"]:
            raise ZhiXueException("登录智学网异常: st 未找到.")

        st = result["data"]["st"]
        return st

    def _login_step_3(self, session: requests.Session, st: str) -> None:
        """登录步骤3：发送登录凭证
        
        Args:
            session: 会话对象
            st: st参数
        """
        logger.info("    [2/6] 发送登录凭证.")
        url = "https://www.zhixue.com/ssoservice.jsp"
        data = {"ticket": st}
        result = session.post(url, params=data).text
        result = result.split("\n", 1)[0]

        if "<!DOCTYPE HTML" in result:
            raise ZhiXueException("登录智学网异常: 服务器 IP 被智学网封禁.")

        if result != "success":
            raise ZhiXueException(f"登录智学网异常: {result}")

        session.cookies.set("uname", base64.b64encode(self.user_account.encode()).decode())


class ZhiXueService:
    """智学网服务类，提供智学网相关功能"""
    
    def __init__(self, message_api=None, user_account=None, password=None, auto_refresh=False):
        """初始化智学网服务
        
        Args:
            message_api: 消息发送API实例
            user_account: 用户账号，可选
            password: 用户密码，可选
            auto_refresh: 是否启用自动刷新，默认为True
        """
        self.message_api = message_api
        self.logger = logging.getLogger(__name__)
        self.session = None
        self.student = None
        self.last_login_time = None
        self.login_lock = asyncio.Lock()
        self.user_account = user_account
        self.password = password
        
        # 启动自动刷新任务
        if auto_refresh:
            asyncio.create_task(self._auto_refresh_login())
        
        # 初始登录
        loop = asyncio.get_event_loop()
        loop.create_task(self._ensure_login())
    
    async def _auto_refresh_login(self):
        """自动刷新登录状态的异步任务"""
        while True:
            await asyncio.sleep(3 * 60 * 60)  # 3小时
            await self._ensure_login(force_refresh=True)
    
    async def _ensure_login(self, force_refresh=False):
        """确保登录状态有效
        
        Args:
            force_refresh: 是否强制刷新登录状态
        """
        async with self.login_lock:
            current_time = time.time()
            
            # 检查是否需要刷新登录
            if not force_refresh and self.session and self.last_login_time:
                # 如果距离上次登录不到3小时且不是强制刷新，则跳过
                if current_time - self.last_login_time < 3 * 60 * 60:
                    return
            
            try:
                # 启动验证码API可执行文件
                start_executable()
                
                # 创建智学网实例并登录
                zhi_xue = ZhiXue(user_account=self.user_account, password=self.password)
                loop = asyncio.get_event_loop()
                with ThreadPoolExecutor() as executor:
                    self.session = await loop.run_in_executor(executor, zhi_xue.login_student)
                    self.student = await loop.run_in_executor(executor, zhixuewang.account.StudentAccount, self.session)
                    await loop.run_in_executor(executor, self.student.set_base_info)
                
                self.last_login_time = current_time
                self.logger.info("智学网登录状态已刷新")
            except Exception as e:
                self.logger.error(f"智学网登录失败: {str(e)}")
                self.session = None
                self.student = None
                raise
    
    async def get_token(self, gid: str, user_account=None, password=None):
        """获取智学网token
        
        Args:
            gid: 群组ID
            user_account: 用户账号，可选
            password: 用户密码，可选
        """
        try:
            await self._ensure_login()
            
            # 获取token
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                token = await loop.run_in_executor(executor, self.student.get_auth_header)
                token = token["XToken"]
            
            # 发送token
            await self.message_api.send_message(gid, token)
            return token
        except Exception as e:
            self.logger.error(f"获取智学网token时出错: {str(e)}")
            await self.message_api.send_message(gid, f"获取token失败: {str(e)}")
            return None
    
    async def get_homework(self, content: str, gid: str, user_account=None, password=None):
        """获取智学网作业
        
        Args:
            content: 命令内容
            gid: 群组ID
            user_account: 用户账号，可选
            password: 用户密码，可选
        """
        try:
            await self._ensure_login()
            
            # 解析作业索引
            try:
                content_index = int(content.strip())
            except ValueError:
                await self.message_api.send_message(gid, "请提供有效的作业索引数字")
                return
            
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                try:
                    # 根据索引获取作业
                    if content_index >= 0:
                        self.logger.info('正在获取所有的作业...')
                        homeworks = await loop.run_in_executor(executor, self.student.get_homeworks, 200, 0)
                        
                        self.logger.info('正在筛选所有的非打卡作业...')
                        clock_homeworks = [hw for hw in homeworks if hw.type.code != 107]
                        
                        if not clock_homeworks:
                            await self.message_api.send_message(gid, "没有非打卡作业。")
                            return
                        
                        if content_index >= len(clock_homeworks):
                            await self.message_api.send_message(gid, f"索引超出范围，最大索引为 {len(clock_homeworks)-1}")
                            return
                        
                        choice = clock_homeworks[content_index]
                    else:
                        # 负数索引表示已完成的作业
                        content_index = -1 - content_index
                        self.logger.info('正在获取所有已完成的作业...')
                        homeworks = await loop.run_in_executor(executor, self.student.get_homeworks, 200, 1)
                        
                        self.logger.info('正在筛选所有的非打卡作业...')
                        clock_homeworks = [hw for hw in homeworks if hw.type.code != 107]
                        
                        if not clock_homeworks:
                            await self.message_api.send_message(gid, "没有已完成的非打卡作业。")
                            return
                        
                        if content_index >= len(clock_homeworks):
                            await self.message_api.send_message(gid, f"索引超出范围，最大索引为 {len(clock_homeworks)-1}")
                            return
                        
                        choice = clock_homeworks[content_index]
                    
                    # 获取作业答案
                    answers = await loop.run_in_executor(executor, self.student.get_homework_answer, choice)
                    answer_content = f"**{choice.title}**\n\n"

                    # 尝试获取均分
                    if choice.type.code == 105:
                        try:
                            session = await loop.run_in_executor(executor, self.student.get_session)
                            stu_class = await loop.run_in_executor(executor, self.student.get_clazz,"")
                            stu_class_id = stu_class.id

                            # 调用API
                            result = await self.get_class_view(
                                session,
                                hw_id=choice.id,
                                class_id=str(stu_class_id),
                                hw_type="105"
                            )

                            if result:
                                # print(result)
                                print("API调用成功")
                                answer_content += f"班级均分: {result['result']['avgScore']} / {result['result']['fullScore']}\n"
                                answer_content += f"班级最高分: {result['result']['maxScore']} / {result['result']['fullScore']}\n"
                            else:
                                print("API调用失败")
                        except Exception as e:
                            self.logger.error(f"获取班级均分时出错: {str(e)}")
                        # finally:
                        #     pass

                    
                    for answer in answers:
                        content = f"**{answer.title}**：{answer.content if answer.content else '暂无答案'}\n\n"
                        answer_content += content
                    
                    await self.message_api.send_message(gid, answer_content)
                except Exception as e:
                    # 如果出现错误，尝试重新登录后重试
                    self.logger.error(f"获取作业时出错，尝试重新登录: {str(e)}")
                    await self._ensure_login(force_refresh=True)
                    
                    # 重新执行相同的操作
                    await self.get_homework(content, gid)
        except Exception as e:
            self.logger.error(f"获取智学网作业时出错: {str(e)}")
            await self.message_api.send_message(gid, f"获取作业失败: {str(e)}")
    
    async def refresh_login(self, gid: str):
        """手动刷新登录状态

        Args:
            gid: 群组ID
        """
        try:
            await self._ensure_login(force_refresh=True)
            await self.message_api.send_message(gid, "智学网登录状态已刷新")
        except Exception as e:
            self.logger.error(f"刷新登录状态时出错: {str(e)}")
            await self.message_api.send_message(gid, f"刷新登录状态失败: {str(e)}")

    async def get_class_view(self, session: requests.Session, hw_id: str, class_id: str, hw_type: str = "105"):
        """获取班级作业视图

        Args:
            session: requests.Session对象
            hw_id: 作业ID
            class_id: 班级ID
            hw_type: 作业类型，默认为"105"

        Returns:
            dict: API响应数据
        """
        url = "https://mhw.zhixue.com/hwreport/class/classView"

        payload = {
            "base": {
                "appId": "OAXI57PG",
                "appVersion": "",
                "sysVersion": "v1001",
                "sysType": "web",
                "packageName": "com.iflytek.edu.hw",
                "udid": "9190943208000048659",
                "expand": {}
            },
            "params": {
                "hwId": hw_id,
                "classId": class_id,
                "hwType": hw_type
            }
        }

        def _make_request():
            """同步请求函数，在线程池中执行"""
            try:
                response = session.get("https://www.zhixue.com/middleweb/newToken")
                headers = {
                    "authorization": response.json().get("result").get("token"),
                }
                # 更新session的header
                session.headers.update(headers)
                response = session.post(url, json=payload)
                if response.status_code == 200:
                    return response.json()
                else:
                    self.logger.error(f"API请求失败，状态码: {response.status_code}")
                    return None
            except Exception as e:
                self.logger.error(f"请求班级作业视图时出错: {str(e)}")
                return None

        try:
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                result = await loop.run_in_executor(executor, _make_request)
                return result
        except Exception as e:
            self.logger.error(f"异步执行请求时出错: {str(e)}")
            return None