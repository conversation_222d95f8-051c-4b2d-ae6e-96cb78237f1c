import asyncio
import os
import sys
from concurrent.futures import ThreadPoolExecutor

import oss2
from app.hwadv_TGT import get_token
import requests
import json
import base64
from typing import Optional, Dict, Any
import eyed3
from mutagen.id3 import ID3, APIC, error
from mutagen.mp3 import MP3
from logger import Logger

# 初始化日志系统
log = Logger()
logger = log.get_logger(filename="music_download")

# LX Music API 配置
API_URL = "https://88.lxmusic.xn--fiqs8s"
API_KEY = "lxmusic"
HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "lx-music-request/3.0",
    "X-Request-Key": API_KEY,
}

def base64_urlsafe(data):
    """Base64 URL安全编码"""
    encoded = base64.b64encode(data.encode("utf-8")).decode("utf-8")
    return encoded.replace("+", "-").replace("/", "_")

class MusicDownloader:
    """音乐下载器类"""
    
    def __init__(self, download_dir: str = "downloaded_music"):
        """初始化下载器
        
        Args:
            download_dir: 下载目录
        """
        self.download_dir = download_dir
        self.api_url = API_URL
        self.headers = HEADERS.copy()
        
        # 确保下载目录存在
        os.makedirs(self.download_dir, exist_ok=True)
        
    def get_music_url(self, source: str, song_id: str, quality: str = "128k") -> Optional[str]:
        """获取音乐播放地址
        
        Args:
            source: 音乐源 (wy, kg, kw, tx, mg)
            song_id: 歌曲ID
            quality: 音质 (128k, 320k, flac, flac24bit)
            
        Returns:
            音乐播放地址或None
        """
        try:
            url = f"{self.api_url}/lxmusicv3/url/{source}/{song_id}/{quality}"
            logger.info(f"请求音乐地址: {url}")
            
            response = requests.get(url, headers=self.headers, timeout=30)
            result = response.json()
            
            logger.info(f"获取音乐地址响应: {result}")
            
            if result.get('code') == 0 and result.get('data'):
                return result['data']
            else:
                logger.error(f"获取音乐地址失败: {result}")
                return None
                
        except Exception as e:
            logger.error(f"获取音乐地址时出错: {str(e)}")
            return None
    
    def download_music(self, music_url: str, filename: str) -> Optional[str]:
        """下载音乐文件
        
        Args:
            music_url: 音乐播放地址
            filename: 文件名（不含扩展名）
            
        Returns:
            下载的文件路径或None
        """
        try:
            # 确保文件名安全
            safe_filename = self._sanitize_filename(filename)
            file_path = os.path.join(self.download_dir, f"{safe_filename}.mp3")
            
            logger.info(f"开始下载音乐: {music_url} -> {file_path}")
            
            # 下载文件
            response = requests.get(music_url, stream=True, timeout=60)
            response.raise_for_status()
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            logger.info(f"音乐下载完成: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"下载音乐时出错: {str(e)}")
            return None
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不安全字符"""
        # 移除或替换不安全的字符
        unsafe_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
        for char in unsafe_chars:
            filename = filename.replace(char, '_')
        
        # 限制文件名长度
        if len(filename) > 100:
            filename = filename[:100]
            
        return filename.strip()

def embed_mp3_metadata(
    mp3_path: str,
    title: str = "",
    lyrics: str = "",
    album: str = "",
    artist: str = "",
    cover_path: str = ""
):
    """嵌入MP3元数据
    
    Args:
        mp3_path: MP3文件路径
        lyrics: 歌词
        album: 专辑名
        artist: 艺术家
        cover_path: 封面图片路径
    """
    try:
        # 处理歌词、专辑、歌手（eyed3）
        audio = eyed3.load(mp3_path)
        if audio.tag is None:
            audio.initTag()
        audio.tag.title = title
        audio.tag.artist = artist
        audio.tag.album = album
        if lyrics:
            audio.tag.lyrics.set(lyrics)
        audio.tag.save()

        # 处理封面图像（mutagen）
        if cover_path and os.path.exists(cover_path):
            audio_mutagen = MP3(mp3_path, ID3=ID3)
            try:
                audio_mutagen.add_tags()
            except error:
                pass
            with open(cover_path, "rb") as img:
                mime_type = "image/jpeg" if cover_path.lower().endswith((".jpg", ".jpeg")) else "image/png"
                audio_mutagen.tags.add(
                    APIC(
                        encoding=3,
                        mime=mime_type,
                        type=3,  # front cover
                        desc="Cover",
                        data=img.read()
                    )
                )
            audio_mutagen.save()

        logger.info(f"✅ 元数据已成功嵌入到 {mp3_path}")
        
    except Exception as e:
        logger.error(f"嵌入元数据时出错: {str(e)}")
        raise

class MusicDownloadManager:
    """音乐下载管理器"""
    
    def __init__(self, download_dir: str = "downloaded_music"):
        """初始化下载管理器"""
        self.downloader = MusicDownloader(download_dir)

    def upload_file_to_zhixue(self, filename, local_file_path):
        def generate_filepath(filename):
            return f"middleHomework/android/zxzy/2025/06/01/musics/{filename}"

        def get_oss_credentials(token, filename):
            url = 'https://aidp.changyan.com/open-svc/file/listStsTokenV2'
            headers = {
                'clientType': 'android',
                'epasAppId': 'zhixue_parent',
                'deviceId': 'a6cfab7da709e438-83ed917048b94f42-ca480ede2110d90e',
                'token': token,
                'Content-Type': 'application/json; charset=utf-8',
                'Accept-Encoding': 'gzip',
                'User-Agent': 'okhttp/3.12.12'
            }
            payload = {
                "stsTokenQueryList": [{
                    "appKey": "XXX_ANDROID_ZXZY_STU",
                    "chunks": 1,
                    "fileName": filename,
                    "productKey": ""
                }]
            }

            try:
                response = requests.post(url, headers=headers, data=json.dumps(payload))
                response.raise_for_status()
                data = response.json()
                return data['data'][0]
            except (requests.RequestException, KeyError, IndexError, json.JSONDecodeError) as e:
                raise RuntimeError(f"Failed to get OSS credentials: {e}")

        def upload_to_oss(oss_info, filepath, local_file_path):
            auth = oss2.StsAuth(
                oss_info['accessId'],
                oss_info['accessSecret'],
                oss_info['securityToken']
            )
            bucket = oss2.Bucket(auth, 'https://oss-cn-hangzhou.aliyuncs.com', 'zhixue-ugc')

            def progress_callback(consumed_bytes, total_bytes):
                if total_bytes:
                    rate = int(100 * (consumed_bytes / total_bytes))
                    print(f'\rUploading: {rate}% ', end='')
                    sys.stdout.flush()

            try:
                result = bucket.put_object_from_file(filepath, local_file_path, progress_callback=progress_callback)
                print(f"\nUpload successful: HTTP Status {result.status}, ETag: {result.etag}")
                return f"https://zhixue-ugc.oss-cn-hangzhou.aliyuncs.com/{filepath}"
            except oss2.exceptions.OssError as e:
                raise RuntimeError(f"Upload failed: {e}")
        token = get_token()
        filepath = generate_filepath(filename)
        oss_info = get_oss_credentials(token, filename)
        return upload_to_oss(oss_info, filepath, local_file_path)

    async def download_song_with_metadata(
        self, 
        song_info: Dict[str, Any], 
        lyrics: str = "",
        cover_url: str = ""
    ) -> Optional[str]:
        """下载歌曲并嵌入元数据
        
        Args:
            song_info: 歌曲信息字典
            lyrics: 歌词
            cover_url: 封面图片URL
            
        Returns:
            下载的文件路径或None
        """
        try:
            # 提取歌曲信息
            song_id = str(song_info.get('songmid', ''))
            song_name = song_info.get('name', '未知歌曲')
            artist = song_info.get('singer', '未知歌手')
            album = song_info.get('albumName', '')
            source = song_info.get('source', 'wy')
            
            # 选择最佳音质
            quality = self._select_best_quality(song_info.get('types', []))
            
            logger.info(f"准备下载: {song_name} - {artist} (质量: {quality})")
            
            # 获取音乐播放地址
            music_url = self.downloader.get_music_url(source, song_id, quality)
            if not music_url:
                raise Exception("无法获取音乐播放地址")
            
            # 下载音乐文件
            filename = f"{song_name} - {artist}"
            file_path = self.downloader.download_music(music_url, filename)
            if not file_path:
                raise Exception("音乐下载失败")
            
            # 下载封面图片（如果有）
            cover_path = None
            if cover_url:
                cover_path = await self._download_cover(cover_url, song_id)
            
            # 嵌入元数据
            embed_mp3_metadata(
                mp3_path="downloaded_music/"+filename+".mp3",
                title=song_name,
                lyrics=lyrics,
                album=album,
                artist=artist,
                cover_path=cover_path
            )
            
            # 清理临时封面文件
            if cover_path and os.path.exists(cover_path):
                try:
                    os.remove(cover_path)
                except:
                    pass
            # 上传文件到oss
            # file_path = self.upload_file_to_zhixue(filename+".mp3", file_path)
            # 在线程池调用同步函数
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                file_url = await loop.run_in_executor(executor, self.upload_file_to_zhixue, filename+".mp3", file_path)
            # 清理本地文件
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass
            return file_url
            
        except Exception as e:
            logger.error(f"下载歌曲时出错: {str(e)}")
            return None
    
    def _select_best_quality(self, types: list) -> str:
        """选择最佳音质"""
        # if not types:
        #     return "128k"
        #
        # # 音质优先级
        # quality_priority = ["flac24bit", "flac", "320k", "128k"]
        #
        # available_qualities = [t.get('type') for t in types if t.get('type')]
        #
        # for quality in quality_priority:
        #     if quality in available_qualities:
        #         return quality
        
        return "128k"
    
    async def _download_cover(self, cover_url: str, song_id: str) -> Optional[str]:
        """下载封面图片
        
        Args:
            cover_url: 封面图片URL
            song_id: 歌曲ID
            
        Returns:
            封面图片路径或None
        """
        try:
            if not cover_url:
                return None
            
            # 确定文件扩展名
            ext = ".jpg"
            if cover_url.lower().endswith(".png"):
                ext = ".png"
            
            cover_path = os.path.join(self.downloader.download_dir, f"cover_{song_id}{ext}")
            
            response = requests.get(cover_url, timeout=30)
            response.raise_for_status()
            
            with open(cover_path, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"封面下载完成: {cover_path}")
            return cover_path
            
        except Exception as e:
            logger.error(f"下载封面时出错: {str(e)}")
            return None
