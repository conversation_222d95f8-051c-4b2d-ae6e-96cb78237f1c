import os

import httpx
import json
import logging
import aiohttp
import base64
import hashlib
import time
import asyncio
from typing import List, Dict, Any, Optional
from concurrent.futures import ProcessPoolExecutor
import markdown
from logger import Logger
from .config import Config

log = Logger()
logger = log.get_logger(filename="api")

class MessageAPI:
    """消息发送API"""
    
    def __init__(self, base_url=None, api_key=None, proxy=None):
        """初始化消息API
        
        Args:
            base_url: API基础URL
            api_key: API密钥
            proxy: 代理服务器地址
        """
        # 创建配置实例
        self.config = Config()
        
        # 从配置获取参数，如果未提供则使用默认值
        self.base_url = base_url or self.config.get_nested('API.vocechat.base_url', 'https://vocechat.xf-yun.cn')
        self.api_key = api_key or self.config.get_nested('API.vocechat.api_key')
        self.proxy = proxy or self.config.get_nested('API.vocechat.proxy', "http://127.0.0.1:7897")


    async def reply_message(self, mid: int, message: str):
        """回复指定消息
        
        Args:
            mid: 被回复的消息ID
            message: 回复的消息内容
            
        Returns:
            响应文本或None（如果发送失败）
        """
        endpoint = f'{self.base_url}/api/bot/reply/{mid}'
        headers = {
            'Content-Type': 'text/markdown',
            'x-api-key': self.api_key,
            'accept': 'application/json; charset=utf-8'
        }

        # 根据消息长度确定超时时间
        message_length = len(message)
        if message_length <= 200:
            timeout = httpx.Timeout(5.0, connect=5.0, read=5.0)
        else:
            timeout = httpx.Timeout(10.0, connect=10.0, read=10.0)
            
        try:
            async with httpx.AsyncClient(timeout=timeout, proxy=self.proxy) as client:
                response = await client.post(endpoint, data=message, headers=headers)
                # logger.info(f"回复消息 {mid}，长度为 {message_length}，消息为 {message}")
                logger.info(f"已回复消息 {mid}: {response.text}")
                return response.text
        except httpx.RequestError as exc:
            logger.error(f"回复消息时发生错误: {exc.request.url!r}. 错误: {exc}")
            return None
        except Exception as e:
            logger.error(f"回复消息时发生未知错误: {str(e)}")
            return None

    async def send_message(self, gid: str, message: str):
        """发送消息到指定群组
        
        Args:
            gid: 群组ID
            message: 要发送的消息内容
            
        Returns:
            响应文本或None（如果发送失败）
        """
        if not gid:
            return
        endpoint = f'{self.base_url}/api/bot/send_to_group/{gid}'
        headers = {
            'Content-Type': 'text/markdown',
            'x-api-key': self.api_key,
            'accept': 'application/json; charset=utf-8'
        }

        # 根据消息长度确定超时时间
        message_length = len(message)
        if message_length <= 200:
            timeout = httpx.Timeout(5.0, connect=5.0, read=5.0)
        else:
            timeout = httpx.Timeout(10.0, connect=10.0, read=10.0)
            
        try:
            async with httpx.AsyncClient(timeout=timeout, proxy=self.proxy) as client:
                response = await client.post(endpoint, data=message, headers=headers)
                logger.info(f"消息已发送到群组 {gid}: {response.text}")
                return response.text
        except httpx.RequestError as exc:
            logger.error(f"发送消息时发生错误: {exc.request.url!r}. 错误: {exc}")
            return None
        except Exception as e:
            logger.error(f"发送消息时发生未知错误: {str(e)}")
            return None
    
    async def send_image(self, gid: str, image_source, filename=None):
        """发送图片到指定群组
        
        Args:
            gid: 群组ID
            image_source: 图片来源，可以是URL、本地文件路径或图片对象
            filename: 文件名，如果未提供则自动生成
            
        Returns:
            响应文本或None（如果发送失败）
        """
        try:
            # 获取图片数据
            image_data = await self._get_image_data(image_source)
            if not image_data:
                logger.error("无法获取图片数据")
                return None
            
            # 确定文件名
            if not filename:
                if isinstance(image_source, str) and (image_source.startswith('http://') or image_source.startswith('https://')):
                    # 从URL提取文件名
                    filename = image_source.split('/')[-1].split('?')[0]
                elif isinstance(image_source, str) and os.path.exists(image_source):
                    # 从本地路径提取文件名
                    filename = os.path.basename(image_source)
                else:
                    # 生成随机文件名
                    filename = f"image_{int(time.time())}.png"
            
            # 确定Content-Type
            content_type = self._get_content_type(filename)
            
            # 第一步：准备上传
            prepare_endpoint = f'{self.base_url}/api/bot/file/prepare'
            prepare_headers = {
                'Content-Type': 'application/json',
                'x-api-key': self.api_key,
                'accept': 'application/json'
            }
            prepare_data = {
                'content_type': content_type,
                'filename': filename
            }
            
            async with httpx.AsyncClient(proxy=self.proxy) as client:
                prepare_response = await client.post(prepare_endpoint, json=prepare_data, headers=prepare_headers)
                prepare_response.raise_for_status()
                file_id = prepare_response.json()
                
                # 第二步：上传文件
                upload_endpoint = f'{self.base_url}/api/bot/file/upload'
                upload_headers = {
                    'x-api-key': self.api_key,
                    'accept': 'application/json'
                }
                
                # 一次性上传文件
                upload_data = {
                    'file_id': file_id,
                    'chunk_data': base64.b64encode(image_data).decode('utf-8'),
                    'chunk_is_last': True
                }
                
                upload_response = await client.post(upload_endpoint, json=upload_data, headers=upload_headers)
                upload_response.raise_for_status()
                upload_result = upload_response.json()
                file_path = upload_result.get('path')
                
                if not file_path:
                    logger.error("上传文件失败，未获取到文件路径")
                    return None
                
                # 第三步：发送图片消息
                send_endpoint = f'{self.base_url}/api/bot/send_to_group/{gid}'
                send_headers = {
                    'Content-Type': 'vocechat/file',
                    'x-api-key': self.api_key,
                    'accept': 'application/json'
                }
                send_data = {
                    'path': file_path
                }
                
                send_response = await client.post(send_endpoint, json=send_data, headers=send_headers)
                send_response.raise_for_status()
                logger.info(f"图片已发送到群组 {gid}: {send_response.text}")
                return send_response.text
                
        except httpx.RequestError as exc:
            logger.error(f"发送图片时发生错误: {exc.request.url!r}. 错误: {exc}")
            return None
        except Exception as e:
            logger.error(f"发送图片时发生未知错误: {str(e)}")
            return None
    
    async def _get_image_data(self, image_source):
        """获取图片数据
        
        Args:
            image_source: 图片来源，可以是URL、本地文件路径或图片对象
            
        Returns:
            图片二进制数据
        """
        try:
            if isinstance(image_source, str):
                if image_source.startswith('http://') or image_source.startswith('https://'):
                    # 从URL下载图片
                    async with httpx.AsyncClient(proxy=self.proxy) as client:
                        response = await client.get(image_source)
                        response.raise_for_status()
                        return response.content
                elif os.path.exists(image_source):
                    # 从本地文件读取图片
                    with open(image_source, 'rb') as f:
                        return f.read()
            elif hasattr(image_source, 'read') and callable(image_source.read):
                # 从文件对象读取
                return image_source.read()
            elif isinstance(image_source, bytes):
                # 直接使用二进制数据
                return image_source
                
            logger.error(f"不支持的图片来源类型: {type(image_source)}")
            return None
        except Exception as e:
            logger.error(f"获取图片数据时出错: {str(e)}")
            return None
    
    def _get_content_type(self, filename):
        """根据文件名确定Content-Type
        
        Args:
            filename: 文件名
            
        Returns:
            Content-Type字符串
        """
        ext = os.path.splitext(filename)[1].lower()
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.webp': 'image/webp',
            '.bmp': 'image/bmp'
        }
        return content_types.get(ext, 'application/octet-stream')


class GPTAPI:
    """GPT API调用类"""
    
    def __init__(self, api_key=None, api_base=None):
        """初始化GPT API
        
        Args:
            api_key: API密钥
            api_base: API基础URL
        """
        # 创建配置实例
        self.config = Config()
        
        # 从配置获取参数，如果未提供则使用默认值
        self.api_key = api_key or self.config.get_nested('API.gpt.api_key', "")
        self.api_base = api_base or self.config.get_nested('API.gpt.api_base', "")
        self.default_model = self.config.get_nested('MODELS.default', "gpt-4o")
        self.default_system_prompt = self.config.get_nested('CONVERSATION.default_system_prompt', "如无特别需求则用中文回复")
    
    async def get_response(self, message: str, model: str = None, conversation_history=None) -> str:
        """调用GPT API获取响应
        
        Args:
            message: 用户消息
            model: 使用的模型名称
            conversation_history: 对话历史记录
            
        Returns:
            GPT响应文本
        """
        try:
            # 使用默认模型（如果未提供）
            if model is None:
                model = self.default_model
                
            # 初始化对话历史（如果未提供）
            if conversation_history is None:
                conversation_history = [{"role": "system", "content": self.default_system_prompt}]
                conversation_history.append({"role": "user", "content": message})

            # 请求URL
            url = f"{self.api_base}/chat/completions"
            
            # 请求头和请求体
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            payload = {
                "model": model,
                "stream": True,  # 启用流式响应
                "messages": conversation_history
            }
            
            # 发送请求并处理响应
            async with httpx.AsyncClient(timeout=None, verify=False) as client:
                response = await client.post(url, json=payload, headers=headers)
                response.raise_for_status()

                # 处理流式响应
                final_response = ""
                async for chunk in response.aiter_text():
                    # logger.debug(f"收到数据块: {chunk}")

                    for line in chunk.splitlines():
                        if not line.startswith("data: "):
                            continue
                            
                        data = line[len("data: "):]
                        if data == "[DONE]":
                            break
                            
                        try:
                            json_data = json.loads(data)
                            completion_text = json_data['choices'][0]['delta'].get('content', '')
                            final_response += completion_text
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析错误: {line}. 错误: {e}")
                            continue

                # 处理最终响应
                processed_response = final_response.replace("\\(", "$").replace("\\)", "$")
                return processed_response.strip()

        except httpx.RequestError as e:
            logger.error(f"HTTP请求失败: {e}")
            return f"网络请求失败: {str(e)}"
        except Exception as e:
            logger.error(f"调用GPT API时出错: {e}")
            return f"GPT接口错误: {str(e)}"
    
    async def get_image_response(self, message: str, model: str = None, imageurl: str = None) -> str:
        """调用支持图像的GPT API获取响应
        
        Args:
            message: 用户消息
            model: 使用的模型名称
            imageurl: 图像URL
            
        Returns:
            GPT响应文本
        """
        try:
            # 如果未提供模型，从配置获取
            if model is None:
                model = self.config.get_nested('MODELS.image.default', "claude-3-5-sonnet-20241022")
                
            # 从配置获取系统提示词
            system_prompt = self.config.get_nested('CONVERSATION.image_system_prompt', 
                                        "如无特别需求则用中文回复，请先仔细分析题目，明确要求后再答题")
            
            # 请求URL
            url = f"{self.api_base}/chat/completions"
            
            # 请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 初始化消息内容
            message_content = [{"type": "text", "text": message}]
            
            # 如果提供了图像URL，则添加到消息内容中
            if imageurl:
                message_content.append({"type": "image_url", "image_url": {"url": imageurl}})
            
            # 请求体
            payload = {
                "model": model,
                "stream": True,  # 启用流式响应
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": message_content}
                ]
            }
            
            # 发送请求并处理响应
            async with httpx.AsyncClient(timeout=None, verify=False) as client:
                response = await client.post(url, json=payload, headers=headers)
                response.raise_for_status()
                
                # 处理流式响应
                final_response = ""
                async for chunk in response.aiter_text():
                    # logger.debug(f"收到数据块: {chunk}")
                    
                    for line in chunk.splitlines():
                        if not line.startswith("data: "):
                            continue
                            
                        data = line[len("data: "):]
                        if data == "[DONE]":
                            break
                            
                        try:
                            json_data = json.loads(data)
                            completion_text = json_data['choices'][0]['delta'].get('content', '')
                            final_response += completion_text
                        except json.JSONDecodeError as e:
                            logger.error(f"JSON解析错误: {line}. 错误: {e}")
                            continue
                
                # 处理最终响应
                processed_response = final_response.replace("\\(", "$").replace("\\)", "$")
                return processed_response.strip()
                
        except httpx.RequestError as e:
            logger.error(f"HTTP请求失败: {e}")
            return f"网络请求失败: {str(e)}"
        except Exception as e:
            logger.error(f"调用GPT API时出错: {e}")
            return f"GPT接口错误: {str(e)}"


class OCRService:
    """OCR服务类"""
    
    def __init__(self, auth_token=None, auth_uuid=None):
        """初始化OCR服务
        
        Args:
            auth_token: 认证令牌
            auth_uuid: 认证UUID
        """
        # 创建配置实例
        self.config = Config()
        
        # 如果没有提供参数，从配置文件获取
        if auth_token is None:
            auth_token = self.config.get_nested('API.ocr.auth_token', "")
        if auth_uuid is None:
            auth_uuid = self.config.get_nested('API.ocr.auth_uuid', "")
            
        self.auth_token = auth_token
        self.auth_uuid = auth_uuid
    
    async def process_image(self, file_url: str) -> str:
        """处理图像并进行OCR识别
        
        Args:
            file_url: 图像文件URL
            
        Returns:
            OCR识别结果文本
        """
        try:
            # 从配置获取OCR API的URL
            ocr_api_url = self.config.get_nested('API.ocr.api_url', "https://web.baimiaoapp.com/api")
            
            async with aiohttp.ClientSession() as session:
                # 步骤1: 下载文件
                async with session.get(file_url) as response:
                    response.raise_for_status()
                    file_data = await response.read()
                    logger.info(f"OCR图片下载成功，大小: {len(file_data)} bytes")
                
                # 步骤2: 计算文件的哈希值
                file_hash = hashlib.sha256(file_data).hexdigest()
                
                # 步骤3: 将文件数据转换为Base64
                file_base64 = base64.b64encode(file_data).decode('utf-8')
                
                # 步骤4: 获取token
                async with session.post(
                    f"{ocr_api_url}/perm/single",
                    headers={
                        "accept": "application/json, text/plain, */*",
                        "content-type": "application/json;charset=UTF-8",
                        "x-auth-token": self.auth_token,
                        "x-auth-uuid": self.auth_uuid,
                    },
                    json={"mode": "single"}
                ) as token_response:
                    token_response.raise_for_status()
                    token = (await token_response.json())['data']['token']
                
                # 步骤5: 提交OCR请求
                async with session.post(
                    f"{ocr_api_url}/ocr/image/baidu",
                    headers={
                        "accept": "application/json, text/plain, */*",
                        "content-type": "application/json;charset=UTF-8",
                        "x-auth-token": self.auth_token,
                        "x-auth-uuid": self.auth_uuid,
                    },
                    json={
                        "token": token,
                        "hash": file_hash,
                        "name": str(int(time.time() * 1000)) + ".png",
                        "size": len(file_data),
                        "dataUrl": f"data:image/jpeg;base64,{file_base64}",
                        "result": {},
                        "status": "processing",
                        "isSuccess": False
                    }
                ) as ocr_response:
                    ocr_response.raise_for_status()
                    jobStatusId = (await ocr_response.json())['data']['jobStatusId']
                
                # 步骤6: 检查OCR状态
                await asyncio.sleep(1)  # 等待处理完成
                async with session.get(
                    f"{ocr_api_url}/ocr/image/baidu/status?jobStatusId={jobStatusId}",
                    headers={
                        "accept": "application/json, text/plain, */*",
                        "x-auth-token": self.auth_token,
                        "x-auth-uuid": self.auth_uuid,
                    }
                ) as status_response:
                    status_response.raise_for_status()
                    
                    # 步骤7: 提取并返回结果
                    words_result = (await status_response.json()).get('data', {}).get('ydResp', {}).get('words_result', [])
                    result = "\n".join(word['words'] for word in words_result)
                    return result
                    
        except aiohttp.ClientError as e:
            logger.error(f"HTTP请求错误: {e}")
            return f"OCR处理失败: HTTP请求错误 - {str(e)}"
        except KeyError as e:
            logger.error(f"键错误: {e}")
            return f"OCR处理失败: 数据解析错误 - {str(e)}"
        except Exception as e:
            logger.error(f"发生了一个意外错误: {e}")
            return f"OCR处理失败: {str(e)}"


class LyricsAPI:
    """歌词搜索API"""
    
    def __init__(self, base_url=None):
        """初始化歌词API
        
        Args:
            base_url: API基础URL
        """
        # 创建配置实例
        self.config = Config()
        
        # 从配置获取参数，如果未提供则使用默认值
        self.base_url = base_url or self.config.get_nested('API.lyrics.base_url', 'https://ncm.nekogan.com')
    
    async def search_lyrics(self, song_name: str) -> str:
        """搜索歌曲歌词
        
        Args:
            song_name: 歌曲名称
            
        Returns:
            歌词文本或错误信息
        """
        try:
            async with aiohttp.ClientSession() as session:
                # 搜索歌曲ID
                search_url = f'{self.base_url}/search?keywords={song_name}'
                async with session.get(search_url) as response:
                    search_data = await response.json()
                    if not search_data.get("result", {}).get("songs"):
                        return f"未找到歌曲: {song_name}"
                    
                    song_id = search_data["result"]["songs"][0]["id"]
                
                # 获取歌词
                lyric_url = f'{self.base_url}/lyric?id={song_id}'
                async with session.get(lyric_url) as response:
                    lyric_data = await response.json()
                    if not lyric_data.get("lrc", {}).get("lyric"):
                        return f"未找到歌曲《{song_name}》的歌词"
                    
                    lyrics = lyric_data["lrc"]["lyric"]
                    
                    # 添加歌曲信息
                    song_info = search_data["result"]["songs"][0]
                    song_title = song_info.get("name", song_name)
                    artists = ", ".join([artist.get("name", "") for artist in song_info.get("artists", [])])
                    
                    formatted_lyrics = f"# 《{song_title}》\n\n**演唱：{artists}**\n\n{lyrics}"
                    return formatted_lyrics
        
        except aiohttp.ClientError as e:
            logger.error(f"HTTP请求错误: {e}")
            return f"歌词接口错误: {str(e)}"
        except KeyError as e:
            logger.error(f"数据解析错误: {e}")
            return f"歌词数据解析错误: {str(e)}"
        except Exception as e:
            logger.error(f"搜索歌词时出错: {e}")
            return f"歌词接口错误，请稍后再试: {str(e)}"


class BaikeAPI:
    """百科查询API"""
    
    def __init__(self, base_url=None):
        """初始化百科API
        
        Args:
            base_url: API基础URL
        """
        # 创建配置实例
        self.config = Config()
        
        # 从配置获取参数，如果未提供则使用默认值
        self.base_url = base_url or self.config.get_nested('API.baike.base_url', 'https://baike.baidu.com')
        
        # 从配置获取请求头
        default_headers = {
            "Host": "baike.baidu.com",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:75.0) Gecko/20100101 Firefox/75.0",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
            "Accept-Encoding": "gzip, deflate",
        }
        self.headers = self.config.get_nested('API.baike.headers', default_headers)
    
    async def search_baike(self, keyword: str) -> str:
        """搜索百科内容
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            百科内容或错误信息
        """
        try:
            async with aiohttp.ClientSession() as session:
                # 初始搜索请求
                initial_url = f"{self.base_url}/lemma/api/entry?word={keyword}&fromModule=lemma_search-box"
                async with session.get(initial_url, headers=self.headers, timeout=5, allow_redirects=False) as initial_response:
                    
                    final_url = ""
                    
                    # 处理重定向
                    if initial_response.status == 302:
                        first_redirect_url = initial_response.headers.get('Location', '')
                        if first_redirect_url:
                            async with session.get("https:" + first_redirect_url, headers=self.headers, timeout=5, allow_redirects=False) as second_response:
                                
                                if second_response.status == 302:
                                    second_redirect_url = second_response.headers.get('Location', '')
                                    if second_redirect_url:
                                        final_url = f"{self.base_url}{second_redirect_url}"
                                else:
                                    final_url = "https:" + first_redirect_url
                    
                    # 如果没有获取到URL，使用默认URL
                    if not final_url:
                        final_url = f"{self.base_url}/item/{keyword}"
                    
                    # 使用ProcessPoolExecutor运行数据获取过程
                    html_content = await self._run_in_process(final_url)
                    return html_content
        
        except asyncio.TimeoutError:
            logger.error("百科请求超时")
            return "请求超时，请稍后再试。"
        except Exception as e:
            logger.error(f"百科查询出错: {e}")
            return f"请求过程中发生错误: {str(e)}"
    
    async def _run_in_process(self, url: str) -> str:
        """在单独的进程中运行数据获取
        
        Args:
            url: 要获取数据的URL
            
        Returns:
            处理后的HTML内容
        """
        loop = asyncio.get_running_loop()
        with ProcessPoolExecutor() as pool:
            logger.info(f"获取百科内容: {url}")
            result = await loop.run_in_executor(pool, self._process_data, url)
        return result
    
    def _process_data(self, url: str) -> str:
        """处理数据获取
        
        Args:
            url: 要获取数据的URL
            
        Returns:
            处理后的HTML内容
        """
        # 在单独的事件循环中运行异步数据获取
        return asyncio.run(self._fetch_and_convert(url))
    
    async def _fetch_and_convert(self, url: str) -> str:
        """获取并转换百科内容
        
        Args:
            url: 要获取数据的URL
            
        Returns:
            处理后的HTML内容
        """
        try:
            # 导入DataHarvest
            from dataharvest.base import DataHarvest
            
            # 创建DataHarvest实例并获取内容
            dh = DataHarvest()
            docs = await dh.a_crawl_and_purify(url)
            md = str(docs.page_content)
            
            # 转换Markdown为HTML
            html = markdown.markdown(md).replace("<h2>基本信息</h2>", "<details><h2>基本信息</h2>") + "</details>"
            return html
        except ImportError:
            logger.error("未安装dataharvest模块")
            return "系统错误: 未安装必要的模块"
        except Exception as e:
            logger.error(f"获取百科内容时出错: {e}")
            return f"获取百科内容时出错: {str(e)}"